#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Database migration script for BasicSwap ping system
Adds instance_id column and removes UNIQUE constraint on addr_from
"""

import sqlite3
import sys
import os

def migrate_ping_database(db_path):
    """Migrate the ping database to support instance IDs"""
    
    if not os.path.exists(db_path):
        print(f"Error: Database file not found: {db_path}")
        return False
    
    print(f"Migrating database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if offerer_ping_status table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='offerer_ping_status'")
        if not cursor.fetchone():
            print("offerer_ping_status table not found - no migration needed")
            conn.close()
            return True
        
        # Check current schema
        cursor.execute('PRAGMA table_info(offerer_ping_status)')
        columns = [col[1] for col in cursor.fetchall()]
        print(f"Current columns: {columns}")
        
        # Check if instance_id column already exists
        if 'instance_id' in columns:
            print("instance_id column already exists")
            
            # Check if we still have the old UNIQUE constraint on addr_from
            cursor.execute('PRAGMA index_list(offerer_ping_status)')
            indexes = cursor.fetchall()
            
            # Check if we need to update the schema (remove old UNIQUE constraint)
            needs_schema_update = False
            for idx in indexes:
                if idx[2] == 1:  # unique index
                    cursor.execute(f'PRAGMA index_info({idx[1]})')
                    index_cols = [col[2] for col in cursor.fetchall()]
                    if index_cols == ['addr_from']:  # Old single-column unique constraint
                        needs_schema_update = True
                        break
            
            if not needs_schema_update:
                print("Database schema is already up to date")
                conn.close()
                return True
        
        print("Updating database schema...")
        
        # Step 1: Create new table with correct schema
        cursor.execute('''
            CREATE TABLE offerer_ping_status_new (
                record_id INTEGER PRIMARY KEY AUTOINCREMENT,
                addr_from STRING,
                instance_id STRING,
                last_ping_received INTEGER,
                ping_failures INTEGER,
                status INTEGER,
                created_at INTEGER,
                updated_at INTEGER,
                UNIQUE(addr_from, instance_id)
            )
        ''')
        print("Created new table with updated schema")
        
        # Step 2: Copy data from old table
        if 'instance_id' in columns:
            # instance_id column exists, copy all columns
            cursor.execute('''
                INSERT INTO offerer_ping_status_new 
                (record_id, addr_from, instance_id, last_ping_received, ping_failures, status, created_at, updated_at)
                SELECT record_id, addr_from, instance_id, last_ping_received, ping_failures, status, created_at, updated_at
                FROM offerer_ping_status
            ''')
        else:
            # instance_id column doesn't exist, add default value
            cursor.execute('''
                INSERT INTO offerer_ping_status_new 
                (record_id, addr_from, instance_id, last_ping_received, ping_failures, status, created_at, updated_at)
                SELECT record_id, addr_from, 'unknown', last_ping_received, ping_failures, status, created_at, updated_at
                FROM offerer_ping_status
            ''')
        print("Copied data to new table")
        
        # Step 3: Drop old table
        cursor.execute('DROP TABLE offerer_ping_status')
        print("Dropped old table")
        
        # Step 4: Rename new table
        cursor.execute('ALTER TABLE offerer_ping_status_new RENAME TO offerer_ping_status')
        print("Renamed new table")
        
        conn.commit()
        print("Migration completed successfully!")
        
        # Show final schema
        cursor.execute('PRAGMA table_info(offerer_ping_status)')
        columns = cursor.fetchall()
        print("Final table schema:")
        for col in columns:
            print(f"  {col[1]} {col[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error during migration: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 migrate_ping_database.py <path_to_basicswap_directory>")
        print("Example: python3 migrate_ping_database.py /path/to/basicswap")
        sys.exit(1)
    
    basicswap_dir = sys.argv[1]
    
    # Try different possible database file names
    possible_db_files = [
        os.path.join(basicswap_dir, "db.sqlite"),
        os.path.join(basicswap_dir, "db_testnet.sqlite"),
        os.path.join(basicswap_dir, "db_regtest.sqlite")
    ]
    
    migrated = False
    for db_path in possible_db_files:
        if os.path.exists(db_path):
            print(f"Found database: {db_path}")
            if migrate_ping_database(db_path):
                migrated = True
            print()
    
    if not migrated:
        print("No database files found to migrate")
        print(f"Checked: {possible_db_files}")
        sys.exit(1)
    
    print("Database migration completed!")

if __name__ == "__main__":
    main()
