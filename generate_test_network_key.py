#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# <PERSON><PERSON>t to generate a new network key for testing ping system between two BasicSwap instances

import os
import sys

# Add the basicswap directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'basicswap'))

from basicswap.contrib.test_framework.key import EC<PERSON>ey
from basicswap.chainparams import chainparams, Coins
from basicswap.util.address import pubkeyToAddress
from basicswap.util import toWIF

def generate_new_network_key():
    """Generate a new network key and address for testing"""
    
    # Generate a new random key
    eckey = ECKey()
    eckey.generate()
    
    # Get the private key in WIF format for mainnet
    wif_prefix = chainparams[Coins.PART]['mainnet']['key_prefix']
    network_key = toWIF(wif_prefix, eckey.get_bytes())
    
    # Get the public key
    network_pubkey = eckey.get_pubkey().get_bytes().hex()
    
    # Calculate the network address
    pubkey_address_prefix = chainparams[Coins.PART]['mainnet']['pubkey_address']
    network_addr = pubkeyToAddress(pubkey_address_prefix, eckey.get_pubkey().get_bytes())
    
    print("=== NEW NETWORK KEY FOR SECOND INSTANCE ===")
    print(f"Network Key (WIF): {network_key}")
    print(f"Network Pubkey: {network_pubkey}")
    print(f"Network Address: {network_addr}")
    print()
    print("=== INSTRUCTIONS ===")
    print("1. Stop your second BasicSwap instance")
    print("2. Edit the basicswap.json file of your second instance")
    print("3. Replace the 'network_key' and 'network_pubkey' values with the ones above")
    print("4. Restart the second instance")
    print("5. Both instances will now have different network identities")
    print()
    print("=== JSON FORMAT ===")
    print(f'    "network_key": "{network_key}",')
    print(f'    "network_pubkey": "{network_pubkey}",')
    print()
    print("After making this change, the ping system will work between your two instances!")

if __name__ == "__main__":
    generate_new_network_key()
